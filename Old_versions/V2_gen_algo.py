import numpy as np
from random import randint, random
from matplotlib import pyplot as plt
import matplotlib.animation as animation
import time,copy

### CONSTANTES #########################################################

I_TYPE_CASE = 0
N_ITERATIONS = 100
DUREE_GARAGE = 50 #s/iterations
PROBA_MUTATION = 0.005
LONGUEUR_PARKING = 10
LARGEUR_PARKING = 10
I_ENTREE = I_SORTIE = LARGEUR_PARKING//2
J_ENTREE, J_SORTIE = 0, LONGUEUR_PARKING-1
DIRECTION_INIT = 1
NB_ROUTES_GEN = 1
NTOT_VOITURES = N_ITERATIONS//3 + 1 # nb total de voitures qui apparaîtront

### FONCTIONS GENERALES ################################################

def parking_cool_test(longueur,largeur):
    
    bords = -np.ones(longueur,dtype=int)

    parking = -np.ones((largeur,longueur),dtype=int)
    
    for x in range(3,longueur-1):
        if x%4 == 1 or x%4 == 2:
            for y in range(2,largeur-2):
                parking[y,x] = 1
        else:
            parking[:,x] = 0 # files vides

    parking[largeur//2],parking[largeur//2-1] = 0,0 # création de la route principale 
    parking[0,3:-1],parking[1,3:-1],parking[-1,3:-1],parking[-2,3:-1] = 0,0,0,0 # création des routes des fins de files

    parking = np.append(bords,parking).reshape(largeur+1,longueur)
    parking = np.append(parking,bords).reshape(largeur+2,longueur)

    T = np.array([True]*longueur*(largeur+2)).reshape(largeur+2,longueur)

    return np.array([parking,T,T,T,T])

def creation_random_parking(longueur,largeur):
    """Crée un parking aléatoirement.

    Parameters
    ----------
    longueur_parking: int
    largeur_parking: int

    Returns
    -------
    array
        tableau numpy LxN composé de quintuplets [genre,dir1,dir2,dir3,dir4]
        genre: int
            entier décrivant la nature du bloc pouvant valoir -1 (mur), 0 (route) ou 1 (place)
        dir1 (resp. dir2, ..): bool
            booléen vrai si la voiture peut avancer devant (resp. à gauche,..) par rapport à l'entrée. [cf fichier paint]

        parking = | [0,True,True,False,False] , ... , [-1,True,True,False,True] |

        Correspondant à :

                    | [0,True,True,False,False]             ...             [-1,True,True,False,True] |
                    |            ...                                                   ...            |
                    |            ...                                                   ...            |
        parking =   |            ...                                                   ...            |
                    |            ...                                                   ...            |
                    |            ...                                                   ...            |
                    | [-1,True,False,True,True]             ...             [-1,True,True,False,True] |

    """

    P = []
    for _ in range(largeur):
        for __ in range(longueur):
            #P.append([1,True,True,True,True])
            P.append([randint(-1,1),True,True,True,True])
            #P.append([randint(-1,1),[True,False][randint(0,1)],[True,False][randint(0,1)],[True,False][randint(0,1)],[True,False][randint(0,1)]])
    
    P[longueur*(largeur//2)][0] = 0 # on impose que l'entrée ne soit pas condamnée
    P[longueur*(largeur//2+1)-1][0] = 0 # de même pour la sortie
    
    P = np.array(P,dtype=list)
    parking = np.empty(5,dtype=list)
    for i in range(5):
        parking[i] = P[:,i].reshape((largeur,longueur))

    for _ in range(NB_ROUTES_GEN):
        ajoute_route(parking,largeur//2,0,largeur//2,longueur-1)

    return parking

def enleve_route(parking,i0,j0,i1,j1,coords_route):
    coords_a_enlever = []
    for j in range(j0,j1):
        for i in range(LARGEUR_PARKING):
            if (i,j) in coords_route:
                parking[0][i,j] = [-1,1][randint(0,1)]

def ajoute_route(parking,i0,j0,i1,j1): # crée une route liant (i0,j0) à (i1,j1)

    dir_choisie = [1,2,4][randint(0,2)]
    i_rempl,j_rempl = i0,j0
    parking[0][i_rempl,j_rempl] = 0
    while i_rempl != i1 or j_rempl != j1:
        parking[dir_choisie][i_rempl,j_rempl] = True # la prochaine case où il y aura de la route est accessible 

        if j_rempl == j1: # si les coordonnées de remplissage de route sont sur la même colonne que le pt d'arrivee
            if i_rempl <= i1: # il monte ou descend pour rejoindre le point d'arrivée
                i_rempl += 1
                dir_choisie = 4
            else:
                i_rempl -= 1
                dir_choisie = 2

        elif dir_choisie == 1 and j_rempl < j1: # sinon il avance s'il peut
            j_rempl += 1
            dir_choisie = [1,2,4][randint(0,2)]

        elif dir_choisie == 2 and i_rempl > 0: # sinon il monte s'il peut
            i_rempl -= 1
            dir_choisie = [1,2][randint(0,1)]

        elif i_rempl < LARGEUR_PARKING-1: # sinon il descend s'il peut
            i_rempl += 1
            dir_choisie = [1,4][randint(0,1)]
        
        elif i_rempl == LARGEUR_PARKING-1: # sauf s'il est arrivé en bas il longe le bords au début
            j_rempl += 1
            dir_choisie = [1,2][randint(0,1)]
        parking[0][i_rempl,j_rempl] = 0

def ajoute_morceau_route(parking,i0,j0,sens_ajout,coords_route): # ajoute un bloc de route au dessus ou en dessus de (i0,j0)
    
    if sens_ajout == 'haut' and j0 == 0: # i0 == 0 non?
        sens_ajout = 'bas'
        (i0,j0) = coordonnees_coupage(i0,j0,parking,sens_ajout,coords_route)

    if sens_ajout == 'bas' and j0 == LARGEUR_PARKING-1:
        sens_ajout = 'haut'
        (i0,j0) = coordonnees_coupage(i0,j0,parking,sens_ajout,coords_route)
    
    if sens_ajout == 'haut':
        parking[0][i0,j0-1] = 0
    else:
        parking[0][i0,j0+1] = 0

def dijkstra(parking, i1, j1, i2, j2):
    """
    Algorithme cherchant avec la méthode de Dijkstra un chemin liant deux points (i1,j1) et (i2,j2) dans arr
    Parameters
    ----------
    parking: array
    i1: int
    j1: int
    i2: int
    j2: int

    Returns
    -------
    array
        liste des coordonnées successives des points du chemin liant (i1,j1) et (i2,j2).
    """
    indices_visités = np.full((LARGEUR_PARKING,LONGUEUR_PARKING), False)
    queue = [(i1, j1)] # queue pour garder les indices que l'on doit visiter
    ind_precedents = {} # dico pour garder les indices précédemment étudiés pour chaque indice
    indices_visités[i1, j1] = True

    while queue: # tant qu'il y a des indices à étudier
        i, j = queue.pop(0) # on prend le prochain indice dans la queue
        if i == i2 and j == j2: # si l'indice actuel est arrivé sur l'indice de destination on arrête le programme
            break

        voisinage = [(i, j+1), (i-1, j), (i, j-1), (i+1, j)] # devant, gauche, derrière, droite

        for num_voisin in range(len(voisinage)):

            i_vois, j_vois = voisinage[num_voisin]

            if 0 <= i_vois < LONGUEUR_PARKING and 0 <= j_vois < LARGEUR_PARKING and not indices_visités[i_vois, j_vois]: # si la case est pas visitée est qu'il n'y a pas de OOB
                accessible = parking[num_voisin+1][i,j] # booléen vrai si la case (i_vois,j_vois) est accessible depuis (i,j)
                if accessible and parking[0][i_vois,j_vois] == 0: # si la case est accessible physiquement et que c'est de la route
                    indices_visités[i_vois, j_vois] = True # le voisin est désormais visité
                    queue.append((i_vois, j_vois)) # on l'ajoute parmi les prochains a devoir être visité

                    ind_precedents[(i_vois, j_vois)] = (i, j) # on garde les indices précédents pour i_vois,j_vois
                accessible = False

    chemin = []

    i, j = i2, j2 # on part de l'arrivée
    while (i, j) in ind_precedents:
        chemin.append((i, j))
        i, j = ind_precedents[(i, j)]

    return list(reversed(chemin))

def directions_dispo(i,j,direction,parking):
    devant_dispo = parking[1][i,j] # /!\ ce n'est pas devant elle mais devant par rapport à l'entrée
    gauche_dispo = parking[2][i,j]
    droite_dispo = parking[3][i,j]
    derriere_dispo = parking[4][i,j]
    
    candidats_dir_dispo = []
    for A in [[devant_dispo,i,j+1],[gauche_dispo,i-1,j],[droite_dispo,i+1,j],[derriere_dispo,i,j-1]]:
        if 0<=A[1]<LARGEUR_PARKING and 0<=A[2]<LONGUEUR_PARKING: # si les coordonnées ne sont pas hors limite
            candidats_dir_dispo.append(A)
    return candidats_dir_dispo            

def etapes_sortie(parking):
    '''
    Fonction renvoyant un dictionnaire avec pour chaque place le chemin à parcourir pour sortir du parking
    '''
    chemins = {}
    coordonnees_places = np.argwhere(parking[0]==1)
    for i_place,j_place in coordonnees_places:
        chemins[(i_place,j_place)] = dijkstra(parking,i_place,j_place,I_SORTIE,J_SORTIE)
    return chemins
    
def simulation(parking):
    """
    Simule les tutures
    """
    chemins_sortie = etapes_sortie(parking)
    voitures = [[[I_ENTREE,J_ENTREE],DIRECTION_INIT,False]] # [(i,j), direction, est_garee]
    parking_ref = copy.deepcopy(parking)
    T_moy_garage , T_moy_sortie = 0 , 0
    
    for i in range(N_ITERATIONS):
        #tab[i] = parking[0] # pour afficher en loop
        if i%3==0 and i != 0: # apparition d'une nouvelle voiture
            voitures.append([[I_ENTREE,J_ENTREE],DIRECTION_INIT,False])

        for num_voiture in range(len(voitures)):
            voiture = voitures[num_voiture]

            if voiture: # si la voiture n'est pas arrivée
                [i,j] = voiture[0]

                if not voiture[2]: # si la voiture n'est pas garée
                    direction = voiture[1]

                    parking[0][voiture[0][0],voiture[0][1]] = np.copy(parking_ref[0])[voiture[0][0],voiture[0][1]]

                    candidats_dir_dispo = directions_dispo(i,j,direction,parking)
                    direction_opposee = direction + 2
                    if direction_opposee > 4:
                        direction_opposee %= 4

                    dir_dispo , dir_place = [] , []

                    for num_dir in range(len(candidats_dir_dispo)): # on teste chaque direction autour de la voiture
                        if num_dir != direction_opposee:
                            i_dir,j_dir = candidats_dir_dispo[num_dir][1],candidats_dir_dispo[num_dir][2]
                            
                            if candidats_dir_dispo[num_dir][0] and 0 <= parking[0][i_dir,j_dir] <= 1: # si la case est accessible règlementairement et physiquement
                                if parking[0][i_dir,j_dir] == 1: # si c'est une place
                                    dir_place.append([candidats_dir_dispo[num_dir][1],candidats_dir_dispo[num_dir][2],num_dir+1])
                                else: # si c'est de la route
                                    dir_dispo.append([candidats_dir_dispo[num_dir][1],candidats_dir_dispo[num_dir][2],num_dir+1])
                    
                    if dir_place != []: # la voiture va en priorité à la place s'il y en a une
                        voiture = [[dir_place[0][0],dir_place[0][1]],dir_place[0][2],True]
                    elif dir_dispo != []: # sinon elle avance aléatoirement là où elle peut
                        choix_alea = randint(0,len(dir_dispo)-1)
                        voiture = [[dir_dispo[choix_alea][0],dir_dispo[choix_alea][1]],dir_dispo[choix_alea][2],False]

                    T_moy_garage += 1/NTOT_VOITURES
                    parking[0] = copy.deepcopy(parking[0]) # éviter aliasing et changer valeur parking_ref
                    parking[0][voiture[0][0],voiture[0][1]] = 2

                else: # si la voiture est garée, elle suit un chemin prédéfini pour sortir
                    if len(voiture) == 3: # si la voiture vient juste de se garer
                        chemin_sortie = chemins_sortie[i,j] # (i,j) et voiture (j,i)
                        voiture.append(chemin_sortie)
                        voiture.append(-DUREE_GARAGE)
                    
                    T_moy_sortie += 1/NTOT_VOITURES
                    if voiture[4] != len(voiture[3]): # si la voiture n'est pas arrivée
                        if voiture[4] > 0: # si la voiture veut partir et qu'il n'y a pas d'autre voiture sur la case
                            [i_nv,j_nv] = voiture[3][voiture[4]]

                            if parking[0][i_nv,j_nv] != 2:
                                parking[0][voiture[0][0],voiture[0][1]] = np.copy(parking_ref[0])[voiture[0][0],voiture[0][1]]
                                voiture[0] = [i_nv,j_nv] # on change les coordonnées de la voiture
                                parking[0] = copy.deepcopy(parking[0])
                                parking[0][voiture[0][0],voiture[0][1]] = 2

                        voiture[4] += 1 # l'indice_etape_sortie augmente de 1

                    else: # si la voiture est arrivée devant la sortie
                        parking[0][i,j] = np.copy(parking_ref[0])[i,j]
                        voiture = [] # il faut trouver un moyen de vanish les tutures
                    
            voitures[num_voiture] = voiture

    if T_moy_sortie == 0: # si malencontreusement aucune voiture n'arrive à sortir
        T_moy_sortie = 100000 # on met un score disqualificatoire
    
    return T_moy_garage,T_moy_sortie,parking_ref

# tester avec 2 dijkstra (un pour trouver place la plus proche puis l'autre pour sortir)

def score(parking): # changer poids avec des exemples jvais voir tqt
    """
    Renvoie le score du parking.
    Ce score est basé sur:
    - la durée moyenne de processus de garage
    - la durée moyenne de sortie du parking
    - le nombre de murs (il faut des arbres)       # À réfléchir...
    - le nombre de places

    PB: cv trop lentement, parking full places trop favorisés

    Parameters
    ----------
    parking: array     # on suppose que le parking n'est plus une array linéaire mais rectangulaire

    Returns
    -------
    score: float
        Note positive = 1/(3*t_moy_garage) + 1/(3*t_moy_sortie) + nb_murs/12 + 3*nb_places/12
        Plus la note est faible, meilleur est le parking.
    parking_ref: array
        On renvoie aussi la configuration du parking vide
    """

    nb_places_acc = len(places_bords_route(parking))
    t_moy_garage,t_moy_sortie,parking_ref = simulation(parking)
    #print(t_moy_garage,t_moy_sortie,nb_places_acc)
    return t_moy_garage + t_moy_sortie - nb_places_acc, parking_ref
    #return -nb_places_acc,parking

def coordonnees_route(parking):
    # algorithme de parcours en profondeur
    i_bal,j_bal = I_ENTREE,J_ENTREE
    cases_visitees = []

    def parcours_profondeur(parking,i_bal,j_bal):
        nonlocal cases_visitees # pour acceder à la variable cases_visitees
        if i_bal < 0 or i_bal >= LARGEUR_PARKING or j_bal < J_ENTREE or j_bal > J_SORTIE:
            return []

        if parking[0][i_bal,j_bal] !=0 or (i_bal,j_bal) in cases_visitees:
            return []
        cases_visitees.append((i_bal,j_bal))
        return [(i_bal,j_bal)] + parcours_profondeur(parking, i_bal+1, j_bal)+parcours_profondeur(parking, i_bal-1, j_bal)+parcours_profondeur(parking, i_bal, j_bal+1)+parcours_profondeur(parking, i_bal, j_bal-1)

    coordos = parcours_profondeur(parking,i_bal,j_bal)
    return coordos

def places_bords_route(parking):
    coords_places = []
    
    for (i_bal,j_bal) in coordonnees_route(parking): # indices de balayage
        coordonnes_entourant = [(i_bal+1,j_bal,4),(i_bal,j_bal+1,1),(i_bal-1,j_bal,2),(i_bal,j_bal-1,3)]
        for (i_ent,j_ent,dir) in coordonnes_entourant:
            if 0<=i_ent<=LARGEUR_PARKING-1 and J_ENTREE<=j_ent<=J_SORTIE and (i_ent,j_ent) not in coords_places and parking[0][i_ent,j_ent] == 1 and parking[dir][i_ent,j_ent]: # s'il y a une place accessible depuis la route
                coords_places.append((i_ent,j_ent))

    return coords_places

def coordonnees_coupage(i_coup,j_coup,parking,lieu_allongement,coords_route): # donne coords sur bordure inf ou sup de la route principale 

    if i_coup == 0 and lieu_allongement == 'haut': # évite oob
        lieu_allongement = 'bas'
    
    if lieu_allongement == 'bas':
        while (i_coup+1,j_coup) in coords_route and i_coup+1 < LARGEUR_PARKING:
            i_coup += 1

        if i_coup+1 == LARGEUR_PARKING: # évite oob
            lieu_allongement = 'haut'
            
    if lieu_allongement == 'haut':
        while (i_coup-1,j_coup) in coords_route and i_coup-1 > 0:
            i_coup -= 1
    return (i_coup,j_coup)
    
def selection(liste_parkings,nb_parkings,etape): #pb poids
    """
    Sélectionne chaque parking avec un nombre de copies proportionnel à son score
    """
    resultats = []

    for num_parking in range(nb_parkings):
        score_parking , parking = score(liste_parkings[num_parking])
        resultats.append([score_parking,parking])

    resultats = np.array(resultats,dtype=object)
    resultats = resultats[resultats[:,0].astype(int).argsort()]
    
    if etape == 'etape1':
        meilleur_score = resultats[0][0]
        coeff_meilleur_parking = 2**len(str(nb_parkings))

        meilleurs_parkings_ponderes = [resultats[0][1]] # on garde d'abord une sauvegarde du meilleur parking
        for i in range(nb_parkings):
            poids = int(coeff_meilleur_parking*resultats[i][0]/meilleur_score)
            if poids >= coeff_meilleur_parking - 1:
                meilleurs_parkings_ponderes += [resultats[i][1]] * poids
            else:
                meilleurs_parkings_ponderes += [resultats[i][1]]

        return meilleurs_parkings_ponderes # on les renvoie d'abord tous car un bon parking mixé avec un mauvais peut être excellent
    
    return [resultats[i][1] for i in range(N_parkings)]

def couplage_croisement(liste_parkings):
    liste_couples =[]
    ind_pere = 0
    ind_mere = 0
    indices_pris = []
    indice_lim = len(liste_parkings) - 1
    while ind_mere < len(liste_parkings):

        ind_mere = ind_pere + 1
        parking_pere = liste_parkings[ind_pere]

        while ind_mere <= indice_lim and (liste_parkings[ind_mere] == parking_pere or (parking_pere,liste_parkings[ind_mere]) in liste_couples):
            ind_mere += 1

        if ind_mere > indice_lim:
            break

        parking_mere = liste_parkings[ind_mere]
        liste_couples.append((parking_pere,parking_mere))

        indices_pris.append(ind_pere)
        indices_pris.append(ind_mere)
        
        while ind_pere in indices_pris:
            ind_pere += 1
        
        if ind_pere > indice_lim:
            break
        
    return liste_couples   

def croisement(liste_parkings):
    """
    Croisement (merci la description)
    """
    liste_couples_parkings = couplage_croisement(liste_parkings)
    grosse_liste_parkings = []

    for (pere, mere) in liste_couples_parkings:
        coordonnees_route_pere, coordonnees_route_mere = coordonnees_route(pere), coordonnees_route(mere)
        fils1, fils2, fille1, fille2 = copy.deepcopy(pere), copy.deepcopy(pere), copy.deepcopy(mere), copy.deepcopy(mere)
        for i in range(LARGEUR_PARKING):
            for j in range(LARGEUR_PARKING):
                if (i,j) in coordonnees_route_pere:
                    fils1[0][i,j] = pere[0][i,j] # le fils 1 hérite de la route du père
                    fils2[0][i,j] = mere[0][i,j] # le fils 2 hérite du reste de la mère
                else:
                    fils1[0][i,j] = pere[0][i,j]
                    fils2[0][i,j] = mere[0][i,j]

                if (i,j) in coordonnees_route_mere:
                    fille1[0][i,j] = pere[0][i,j] # la fille 1 hérite de la route de la mère
                    fille2[0][i,j] = mere[0][i,j] # la fille 2 hérite du reste du père
                else:
                    fille1[0][i,j] = pere[0][i,j]
                    fille2[0][i,j] = mere[0][i,j]
        
        grosse_liste_parkings += [pere,mere,fils1,fils2,fille1,fille2]
    return grosse_liste_parkings
    
def mutation(liste_parkings):
    """
    Mutation (meilleure description)
    """

    for num_parking in range(1,len(liste_parkings)): # on ne mute pas le premier pour le garder intact
        parking = liste_parkings[num_parking]
        coords_route = coordonnees_route(parking)

        # déviation d'une partie de la route
        if len(coords_route) == 0:
            (i_coup1,j_coup1), (i_coup2,j_coup2) = (I_ENTREE,J_ENTREE), (I_SORTIE,J_SORTIE)
            ajoute_route(parking,i_coup1,j_coup1,i_coup2,j_coup2)
            coords_route = coordonnees_route(parking)

        else:
            (i_coup1,j_coup1) , (i_coup2,j_coup2) = coords_route[randint(0,len(coords_route)-1)] , coords_route[randint(0,len(coords_route)-1)] # coords prises aléatoirement sur route principale
            sens_allongement = ['haut','bas'][randint(0,1)] # on ajoute de la route en haut ou en bas
            (i_coup1,j_coup1) , (i_coup2,j_coup2) = coordonnees_coupage(i_coup1,j_coup1,parking,sens_allongement,coords_route) , coordonnees_coupage(i_coup2,j_coup2,parking,sens_allongement,coords_route)

            if j_coup1 <= j_coup2:
                enleve_route(parking,i_coup1,j_coup1,i_coup2,j_coup2,coords_route)
                ajoute_route(parking,i_coup1,j_coup1,i_coup2,j_coup2)
            else:
                enleve_route(parking,i_coup2,j_coup2,i_coup1,j_coup1,coords_route)
                ajoute_route(parking,i_coup2,j_coup2,i_coup1,j_coup1)

        # ajout d'un morceau de la route
        (i_ajout,j_ajout) = coords_route[randint(0,len(coords_route)-1)]
        sens_ajout = ['haut','bas'][randint(0,1)] # on ajoute de la route en haut ou en bas
        (i_ajout,j_ajout) = coordonnees_coupage(i_ajout,j_ajout,parking,sens_ajout,coords_route)
        ajoute_morceau_route(parking,i_ajout,j_ajout,sens_ajout,coords_route)
        
        liste_parkings[num_parking] = parking

def evolution(N_parkings,N_generations):
    '''
    Algorithme génétique pour une population de N parking.
    '''
    tab = np.zeros((N_generations,LARGEUR_PARKING,LONGUEUR_PARKING))
    pop_parkings = [creation_random_parking(LONGUEUR_PARKING,LARGEUR_PARKING)for _ in range(N_parkings)] # population initiale: on crée N parkings (durée: 0.06s)

    for _ in range(N_generations):
        pop_parkings = selection(pop_parkings,N_parkings,'etape1')
        pop_parkings = croisement(pop_parkings)
        mutation(pop_parkings)
        print(len(pop_parkings))
        pop_parkings = selection(pop_parkings,len(pop_parkings),'etape2')
        tab[_] = pop_parkings[0][0] # affichage de l'évolution
    
    meilleur_parking = pop_parkings[0]
    
    affichage_loop(tab)
    affichage(meilleur_parking)
    
    return meilleur_parking

### FONCTION D'AFFICHAGE ###############################################

def affichage(tab):

    tab = tab[0] # on ne prend que les premiers indices pour l'afficher
    tab = np.array(tab,dtype=int)

    # Initialisation fenetre
    fig, ax = plt.subplots(figsize=(0.2*LARGEUR_PARKING, 0.2*LARGEUR_PARKING))

    # Dessin du tableau
    image = plt.imshow(tab, vmin=-1, vmax=2)
    
    ax = plt.gca()
    ax.get_xaxis().set_visible(False)
    ax.get_yaxis().set_visible(False)
    plt.set_cmap('BrBG') #gist_gray ou bone

    plt.show()

def affichage_loop(tab):
    # Initialisation fenetre
    fig, ax = plt.subplots(figsize=(0.2*LARGEUR_PARKING, 0.2*LARGEUR_PARKING))

    # Dessin du tableau
    image = plt.imshow(tab[0], vmin=-1, vmax=2)
    # Animation
    ani = animation.FuncAnimation(fig, update_ani, frames = tab.shape[0], interval = int(1000*5/tab.shape[0]), fargs=(tab, image), repeat = True)

    ax = plt.gca()
    ax.get_xaxis().set_visible(False)
    ax.get_yaxis().set_visible(False)
    plt.set_cmap('BrBG') #gist_gray ou bone

    plt.show()

def update_ani(i, tab, image):

    image.set_data(tab[i]) #Mise à jour de l'image
    
    return True

### PROGRAMME PRINCIPAL ################################################

N_parkings = 100
N_generations = 20

parking_final = evolution(N_parkings,N_generations)

"""tab = np.zeros((N_ITERATIONS,largeur_parking+2,longueur_parking))
P_test = parking_cool_test(longueur_parking,largeur_parking)
print(score(P_test))
affichage_loop(tab)"""
